import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Form, Button, Table, Card, Spinner, Dropdown, Toast } from 'react-bootstrap';
import { FaCalendarAlt, FaUserTie, FaChartBar, FaFileExport, FaSearch, FaFileExcel, FaFilePdf, FaChevronRight, FaChevronDown } from 'react-icons/fa';
import API from "../../CustomHooks/MasterApiHooks/api";
import { exportUserReportToExcel, exportUserReportToPDF } from './Userexport/UserReportExport';

const Userreport = () => {
    // State variables
    const [startDate, setStartDate] = useState(() => {
        const today = new Date();
        const yyyy = today.getFullYear();
        const mm = String(today.getMonth() + 1).padStart(2, '0');
        const dd = String(today.getDate()).padStart(2, '0');
        return `${yyyy}-${mm}-${dd}`;
    });
    const [endDate, setEndDate] = useState("");
    const [reportType, setReportType] = useState('summary');
    const [selectedUser, setSelectedUser] = useState('all');
    const [users, setUsers] = useState([]);
    const [allUsers, setAllUsers] = useState([]); // Store all users for ID mapping
    const [processes, setProcesses] = useState([]); // Store processes for name mapping
    const [userReportData, setUserReportData] = useState([]);
    const [expandedGroups, setExpandedGroups] = useState(new Set()); // Track expanded groups
    const [projectData, setProjectData] = useState({}); // Store project data for each group
    const [loadingProjects, setLoadingProjects] = useState(new Set()); // Track loading state for projects
    const [expandedProjects, setExpandedProjects] = useState(new Set()); // Track expanded projects
    const [lotData, setLotData] = useState({}); // Store lot data for each project
    const [loadingLots, setLoadingLots] = useState(new Set()); // Track loading state for lots
    const [isLoading, setIsLoading] = useState(false);
    const [isLoadingUsers, setIsLoadingUsers] = useState(false);
    const [showTable, setShowTable] = useState(false);
    const [showToast, setShowToast] = useState(false);
    const [toastMessage, setToastMessage] = useState('');

    // Format date for API (DD-MM-YYYY format)
    const formatDateForApi = (dateString) => {
        if (!dateString) return '';
        const date = new Date(dateString);
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        return `${day}-${month}-${year}`;
    };

    // Fetch all users and processes for mapping on component mount
    useEffect(() => {
        const fetchInitialData = async () => {
            try {
                // Fetch all users for ID mapping
                const userResponse = await API.get('/User');

                // Create a mapping of userName to userId
                const usersData = userResponse.data;
                console.log('All users data:', usersData);
                console.log('Sample user structure:', usersData[0]);

                setAllUsers(usersData);

                // Fetch all processes for name mapping
                const processResponse = await API.get('/Processes');
                setProcesses(processResponse.data);
            } catch (error) {
                console.error('Error fetching initial data:', error);
                setAllUsers([]);
                setProcesses([]);
            }
        };

        fetchInitialData();
    }, []);

    // Fetch users based on selected date
    useEffect(() => {
        const fetchUsersForDate = async () => {
            if (allUsers.length === 0) return; // Wait for allUsers to be loaded

            setIsLoadingUsers(true);
            try {
                if (!startDate) {
                    // If no date is selected, show "All Users" option only
                    setUsers([{ id: 'all', name: 'All Users', userId: null }]);
                    setIsLoadingUsers(false);
                    return;
                }

                // Fetch user summary data for the selected date to get available supervisors
                const params = new URLSearchParams();
                params.append('date', formatDateForApi(startDate));

                const response = await API.get(`/Reports/User-Wise-Summary?${params.toString()}`);
                const summaryData = response.data.userWiseSummary || [];

                if (summaryData.length === 0) {
                    // If no data for this date, show "All Users" option only
                    setUsers([{ id: 'all', name: 'All Users', userId: null }]);
                    setIsLoadingUsers(false);
                    return;
                }

                // Extract unique supervisors from the summary data and map to user IDs
                const availableUsers = summaryData.map(item => {
                    // Find the user by matching userName with supervisor
                    const user = allUsers.find(u => u.userName === item.supervisor);

                    // Determine the actual user ID to use for API calls
                    let actualUserId = null;
                    if (user) {
                        // Try different possible ID fields
                        actualUserId = user.id || user.userId || user.ID || user.UserId;
                    }

                    return {
                        id: item.supervisor, // Use supervisor name as id for selection
                        name: item.supervisor,
                        userId: actualUserId, // Store the actual numeric user ID
                        originalUser: user // Store the full user object for debugging
                    };
                });

                // Add "All Users" option at the beginning
                const userOptions = [
                    { id: 'all', name: 'All Users', userId: null },
                    ...availableUsers
                ];

                setUsers(userOptions);

                // Reset selected user to 'all' when date changes
                setSelectedUser('all');

            } catch (error) {
                console.error('Error fetching users for date:', error);
                setUsers([{ id: 'all', name: 'All Users', userId: null }]);
            } finally {
                setIsLoadingUsers(false);
            }
        };

        fetchUsersForDate();
    }, [startDate, allUsers]); // Re-run when startDate or allUsers changes

    // Helper function to get process name by ID
    const getProcessName = (processId) => {
        if (!processId) return 'N/A';
        const process = processes.find(p => p.id === processId);
        return process ? process.name : `Process ${processId}`;
    };

    // Helper function to display values (hide 0, null, undefined, empty string)
    const displayValue = (value) => {
        if (value === null || value === undefined || value === 0 || value === '' || value === '0') {
            return '';
        }
        return value;
    };

    // Function to handle group row expansion
    const handleGroupExpansion = async (groupId) => {
        const newExpandedGroups = new Set(expandedGroups);

        if (expandedGroups.has(groupId)) {
            // Collapse the group
            newExpandedGroups.delete(groupId);
            setExpandedGroups(newExpandedGroups);
        } else {
            // Expand the group and fetch project data
            newExpandedGroups.add(groupId);
            setExpandedGroups(newExpandedGroups);

            // Check if we already have project data for this group
            if (!projectData[groupId]) {
                await fetchProjectData(groupId);
            }
        }
    };

    // Function to handle project row expansion
    const handleProjectExpansion = async (groupId, projectId) => {
        const projectKey = `${groupId}-${projectId}`;
        const newExpandedProjects = new Set(expandedProjects);

        if (expandedProjects.has(projectKey)) {
            // Collapse the project
            newExpandedProjects.delete(projectKey);
            setExpandedProjects(newExpandedProjects);
        } else {
            // Expand the project and fetch lot data
            newExpandedProjects.add(projectKey);
            setExpandedProjects(newExpandedProjects);

            // Check if we already have lot data for this project
            if (!lotData[projectKey]) {
                await fetchLotData(groupId, projectId);
            }
        }
    };

    // Function to fetch lot data for a specific project
    const fetchLotData = async (groupId, projectId) => {
        const projectKey = `${groupId}-${projectId}`;
        const newLoadingLots = new Set(loadingLots);
        newLoadingLots.add(projectKey);
        setLoadingLots(newLoadingLots);

        try {
            const params = new URLSearchParams();

            // Add the same parameters as the main details call
            if (selectedUser !== 'all') {
                const selectedUserObj = users.find(u => u.id === selectedUser);
                if (selectedUserObj && selectedUserObj.userId) {
                    params.append('userId', selectedUserObj.userId);
                } else {
                    const matchedUser = allUsers.find(u => u.userName === selectedUser);
                    if (matchedUser) {
                        const userId = matchedUser.id || matchedUser.userId || matchedUser.ID || matchedUser.UserId;
                        if (userId) {
                            params.append('userId', userId);
                        }
                    }
                }
            }

            if (startDate && !endDate) {
                params.append('date', formatDateForApi(startDate));
            } else if (startDate && endDate) {
                params.append('startDate', formatDateForApi(startDate));
                params.append('endDate', formatDateForApi(endDate));
            }

            // Add groupId and projectId parameters for lot-level data
            params.append('groupId', groupId);
            params.append('projectId', projectId);
            params.append('page', '1');
            params.append('pageSize', '1000');

            const response = await API.get(`/Reports/User-Wise?${params.toString()}`);
            const lotsData = response.data.lotNoWiseSummary || [];

            // Transform lot data to include process names
            const transformedLots = lotsData.map(lot => ({
                ...lot,
                processNames: lot.processIds ? lot.processIds.map(id => getProcessName(id)) : []
            }));

            // Store lot data for this project
            setLotData(prev => ({
                ...prev,
                [projectKey]: transformedLots
            }));

        } catch (error) {
            console.error('Error fetching lot data for project:', projectId, error);
            setToastMessage('Error fetching lot data');
            setShowToast(true);
        } finally {
            const newLoadingLots = new Set(loadingLots);
            newLoadingLots.delete(projectKey);
            setLoadingLots(newLoadingLots);
        }
    };

    // Function to fetch project data for a specific group
    const fetchProjectData = async (groupId) => {
        const newLoadingProjects = new Set(loadingProjects);
        newLoadingProjects.add(groupId);
        setLoadingProjects(newLoadingProjects);

        try {
            const params = new URLSearchParams();

            // Add the same parameters as the main details call
            if (selectedUser !== 'all') {
                const selectedUserObj = users.find(u => u.id === selectedUser);
                if (selectedUserObj && selectedUserObj.userId) {
                    params.append('userId', selectedUserObj.userId);
                } else {
                    const matchedUser = allUsers.find(u => u.userName === selectedUser);
                    if (matchedUser) {
                        const userId = matchedUser.id || matchedUser.userId || matchedUser.ID || matchedUser.UserId;
                        if (userId) {
                            params.append('userId', userId);
                        }
                    }
                }
            }

            if (startDate && !endDate) {
                params.append('date', formatDateForApi(startDate));
            } else if (startDate && endDate) {
                params.append('startDate', formatDateForApi(startDate));
                params.append('endDate', formatDateForApi(endDate));
            }

            // Add groupId parameter for project-level data
            params.append('groupId', groupId);
            params.append('page', '1');
            params.append('pageSize', '1000');

            const response = await API.get(`/Reports/User-Wise?${params.toString()}`);
            const projectsData = response.data.projectWiseSummary || [];

            // Transform project data to include process names
            const transformedProjects = projectsData.map(project => ({
                ...project,
                processNames: project.processIds ? project.processIds.map(id => getProcessName(id)) : []
            }));

            // Store project data for this group
            setProjectData(prev => ({
                ...prev,
                [groupId]: transformedProjects
            }));

        } catch (error) {
            console.error('Error fetching project data for group:', groupId, error);
            setToastMessage('Error fetching project data');
            setShowToast(true);
        } finally {
            const newLoadingProjects = new Set(loadingProjects);
            newLoadingProjects.delete(groupId);
            setLoadingProjects(newLoadingProjects);
        }
    };

    // Handle View Report button click
    const handleViewReport = async () => {
        if (!startDate) {
            setToastMessage('Please select a start date');
            setShowToast(true);
            return;
        }

        if (!reportType) {
            setToastMessage('Please select a report type');
            setShowToast(true);
            return;
        }

        if (selectedUser === '') {
            setToastMessage('Please select a user');
            setShowToast(true);
            return;
        }

        setIsLoading(true);
        setShowTable(false);
        setExpandedGroups(new Set());
        setProjectData({});
        setExpandedProjects(new Set());
        setLotData({});

        try {
            let apiUrl = '';
            let params = new URLSearchParams();

            if (reportType === 'summary') {
                // Use User-Wise-Summary endpoint
                apiUrl = '/Reports/User-Wise-Summary';

                // For summary, we filter by supervisor name, not userId
                if (startDate && !endDate) {
                    params.append('date', formatDateForApi(startDate));
                } else if (startDate && endDate) {
                    params.append('startDate', formatDateForApi(startDate));
                    params.append('endDate', formatDateForApi(endDate));
                }

            } else {
                // Use User-Wise endpoint for details
                apiUrl = '/Reports/User-Wise';

                // For details report, we need to pass the userId parameter
                if (selectedUser !== 'all') {
                    const selectedUserObj = users.find(u => u.id === selectedUser);
                    console.log('Selected user object:', selectedUserObj);

                    if (selectedUserObj && selectedUserObj.userId) {
                        params.append('userId', selectedUserObj.userId);
                        console.log('Adding userId to params:', selectedUserObj.userId);
                    } else {
                        console.log('No valid userId found for selected user:', selectedUser);
                        // Try to find the user in allUsers by userName
                        const matchedUser = allUsers.find(u => u.userName === selectedUser);
                        if (matchedUser) {
                            // Try different possible ID fields
                            const userId = matchedUser.id || matchedUser.userId || matchedUser.ID || matchedUser.UserId;
                            if (userId) {
                                params.append('userId', userId);
                                console.log('Adding userId from allUsers:', userId);
                            }
                        }
                    }
                }

                if (startDate && !endDate) {
                    params.append('date', formatDateForApi(startDate));
                } else if (startDate && endDate) {
                    params.append('startDate', formatDateForApi(startDate));
                    params.append('endDate', formatDateForApi(endDate));
                }

                params.append('page', '1');
                params.append('pageSize', '1000');
            }

            const fullUrl = `${apiUrl}?${params.toString()}`;
            console.log('Making API call to:', fullUrl);

            const response = await API.get(fullUrl);
            console.log('API response:', response.data);

            if (reportType === 'summary') {
                let summaryData = response.data.userWiseSummary || [];

                // Filter by selected user if not 'all'
                if (selectedUser !== 'all') {
                    summaryData = summaryData.filter(item => item.supervisor === selectedUser);
                }

                if (summaryData.length === 0) {
                    setToastMessage('No data found for the selected criteria');
                    setShowToast(true);
                } else {
                    setUserReportData(summaryData);
                    setShowTable(true);
                }
            } else {
                const detailsData = response.data.groupWiseSummary || [];
                if (detailsData.length === 0) {
                    setToastMessage('No data found for the selected criteria');
                    setShowToast(true);
                } else {
                    // Transform the data to include process names
                    const transformedData = detailsData.map(item => ({
                        ...item,
                        processNames: item.processIds ? item.processIds.map(id => getProcessName(id)) : []
                    }));
                    setUserReportData(transformedData);
                    setShowTable(true);
                }
            }

        } catch (error) {
            console.error('Error fetching user report data:', error);
            setToastMessage('Error fetching report data. Please try again.');
            setShowToast(true);
        } finally {
            setIsLoading(false);
        }
    };

    // Export handlers
    const handleExcelExport = () => {
        try {
            const filters = {
                startDate,
                endDate,
                selectedUser,
                reportType
            };

            const expandedData = {
                projectData,
                lotData,
                expandedGroups,
                expandedProjects
            };

            const result = exportUserReportToExcel(userReportData, reportType, filters, expandedData);

            if (result.success) {
                setToastMessage(result.message);
                setShowToast(true);
            } else {
                setToastMessage(result.message);
                setShowToast(true);
            }
        } catch (error) {
            console.error('Export error:', error);
            setToastMessage('Error exporting to Excel');
            setShowToast(true);
        }
    };

    const handlePDFExport = () => {
        try {
            const filters = {
                startDate,
                endDate,
                selectedUser,
                reportType
            };

            const expandedData = {
                projectData,
                lotData,
                expandedGroups,
                expandedProjects
            };

            const result = exportUserReportToPDF(userReportData, reportType, filters, expandedData);

            if (result.success) {
                setToastMessage(result.message);
                setShowToast(true);
            } else {
                setToastMessage(result.message);
                setShowToast(true);
            }
        } catch (error) {
            console.error('Export error:', error);
            setToastMessage('Error exporting to PDF');
            setShowToast(true);
        }
    };

    // Reset form
    const handleReset = () => {
        setStartDate(new Date().toISOString().split('T')[0]);
        setEndDate('');
        setReportType('summary');
        setSelectedUser('all');
        setShowTable(false);
        setUserReportData([]);
        setExpandedGroups(new Set());
        setProjectData({});
        setLoadingProjects(new Set());
        setExpandedProjects(new Set());
        setLotData({});
        setLoadingLots(new Set());
    };

    return (
        <Container fluid className="px-2 px-md-3 px-lg-4">
           

            {/* Filters Section */}
            <Row className="mb-4">
                <Col xs={12}>
                    <Card>
                        <Card.Body className="p-3">
                            <Row className="g-2 g-md-3">
                                {/* Date Range - From */}
                                <Col xs={6} sm={6} md={4} lg={2}>
                                    <Form.Group>
                                        <Form.Label className="small fw-semibold mb-1">
                                            <FaCalendarAlt className="me-1" size={12} />
                                            <span className="text-danger">*</span> From
                                        </Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={startDate}
                                            onChange={(e) => setStartDate(e.target.value)}
                                            size="sm"
                                            style={{ height: '32px' }}
                                        />
                                    </Form.Group>
                                </Col>

                                {/* Date Range - To */}
                                <Col xs={6} sm={6} md={4} lg={2}>
                                    <Form.Group>
                                        <Form.Label className="small fw-semibold mb-1">
                                            <FaCalendarAlt className="me-1" size={12} />
                                            To
                                        </Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={endDate}
                                            onChange={(e) => setEndDate(e.target.value)}
                                            size="sm"
                                            style={{ height: '32px' }}
                                        />
                                    </Form.Group>
                                </Col>

                                {/* Report Type */}
                                <Col xs={6} sm={6} md={4} lg={1}>
                                    <Form.Group>
                                        <Form.Label className="small fw-semibold mb-1">
                                            <FaChartBar className="me-1" size={12} />
                                            <span className="text-danger">*</span> Report Type
                                        </Form.Label>
                                        <Form.Select
                                            value={reportType}
                                            onChange={(e) => setReportType(e.target.value)}
                                            size="sm"
                                            style={{ height: '32px' }}
                                        >
                                            <option value="summary">Summary</option>
                                            <option value="details">Details</option>
                                        </Form.Select>
                                    </Form.Group>
                                </Col>

                                {/* User Selection */}
                                <Col xs={6} sm={6} md={4} lg={1}>
                                    <Form.Group>
                                        <Form.Label className="small fw-semibold mb-1">
                                            <FaUserTie className="me-1" size={12} />
                                            <span className="text-danger">*</span> User
                                        </Form.Label>
                                        <Form.Select
                                            value={selectedUser}
                                            onChange={(e) => setSelectedUser(e.target.value)}
                                            size="sm"
                                            style={{ height: '32px' }}
                                            disabled={isLoadingUsers}
                                        >
                                            {isLoadingUsers ? (
                                                <option>Loading users...</option>
                                            ) : (
                                                users.map(user => (
                                                    <option key={user.id} value={user.id}>
                                                        {user.name}
                                                    </option>
                                                ))
                                            )}
                                        </Form.Select>
                                    </Form.Group>
                                </Col>

                                {/* Action Buttons */}
                                <Col xs={12} sm={12} md={4} lg={2} className="d-flex align-items-end">
                                    <div className="d-flex gap-2 w-100 justify-content-md-end mt-2 mt-md-0">
                                        <Button
                                            variant="primary"
                                            size="sm"
                                            onClick={handleViewReport}
                                            disabled={isLoading || !startDate || !reportType || selectedUser === ''}
                                            style={{ height: '32px' }}
                                            className="flex-grow-1 flex-md-grow-0"
                                        >
                                            {isLoading ? (
                                                <>
                                                    <Spinner size="sm" className="me-1" />
                                                    Loading...
                                                </>
                                            ) : (
                                                <>
                                                    <FaSearch className="me-1" size={12} />
                                                    View Report
                                                </>
                                            )}
                                        </Button>
                                        <Button
                                            variant="outline-secondary"
                                            size="sm"
                                            onClick={handleReset}
                                            style={{ height: '32px' }}
                                            className="flex-grow-1 flex-md-grow-0"
                                        >
                                            Reset
                                        </Button>
                                    </div>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Data Table */}
            <Row>
                <Col xs={12}>
                    {showTable && (
                        <Card>
                            <Card.Header className="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                                <h6 className="mb-2 mb-md-0">
                                    {reportType === 'summary' ? 'User Summary Report' : 'User Details Report'}
                                </h6>
                                <div className="d-flex gap-2">
                                    <Dropdown>
                                        <Dropdown.Toggle  size="sm">
                                            <FaFileExport size={30} className="me-1" />
                                        </Dropdown.Toggle>
                                        <Dropdown.Menu>
                                            <Dropdown.Item onClick={handlePDFExport} className="text-center">
                                                <FaFilePdf size={30} style={{ color: '#DC3545' }} />
                                            </Dropdown.Item>
                                            <Dropdown.Item onClick={handleExcelExport} className="text-center">
                                                <FaFileExcel size={30} style={{ color: '#217346' }} />
                                            </Dropdown.Item>
                                            
                                        </Dropdown.Menu>
                                    </Dropdown>
                                </div>
                            </Card.Header>
                            <Card.Body className="p-0 p-md-3">
                                <div className="table-responsive">
                                    {reportType === 'summary' ? (
                                        <Table striped bordered hover size="sm" className="mb-0">
                                            <thead className="table-dark">
                                                <tr style={{ textAlign: 'center' }}>
                                                    <th>Supervisor</th>
                                                     <th>Groups</th>
                                                    <th>Projects</th>
                                                    <th>Lot No</th>
                                                    <th>Catch</th>
                                                    <th>Total Quantity</th>
                                                   
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {userReportData.map((item, index) => (
                                                    <tr key={index} style={{ textAlign: 'center' }}>
                                                        <td>{item.supervisor}</td>
                                                        <td>{displayValue(item.countOfGroupIds)}</td>
                                                        <td>{displayValue(item.countIfProjectIds)}</td>
                                                        <td>{displayValue(item.countOfLotNo)}</td>
                                                        <td>{displayValue(item.catchCount)}</td>
                                                        <td>{displayValue(item.totalQuantity)}</td>
                                                        
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </Table>
                                    ) : (
                                    <Table striped bordered hover size="sm" className="mb-0" style={{ width: '100%' }}>
                                        <thead className="table-dark">
                                            {/* Main Headers */}
                                            <tr style={{ textAlign: 'center', fontSize: '0.875rem' }}>
                                                <th rowSpan={2} style={{ minWidth: '150px', verticalAlign: 'middle' }}>Groups</th>
                                                <th colSpan={2} style={{ minWidth: '120px' }}>Paper</th>
                                                <th colSpan={2} style={{ minWidth: '120px' }}>Booklet</th>
                                                <th rowSpan={2} style={{ minWidth: '200px', verticalAlign: 'middle' }}>Process</th>
                                            </tr>
                                            {/* Sub Headers */}
                                            <tr style={{ textAlign: 'center', fontSize: '0.875rem' }}>
                                                <th style={{ minWidth: '60px' }}>Catch</th>
                                                <th style={{ minWidth: '60px' }}>Quantity</th>
                                                <th style={{ minWidth: '60px' }}>Catch</th>
                                                <th style={{ minWidth: '60px' }}>Quantity</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {userReportData.map((group, index) => (
                                                <React.Fragment key={index}>
                                                    {/* Group Row - Clickable */}
                                                    <tr
                                                        style={{
                                                            cursor: 'pointer',
                                                            backgroundColor: expandedGroups.has(group.groupId) ? '#f8f9fa' : 'inherit'
                                                        }}
                                                        onClick={() => handleGroupExpansion(group.groupId)}
                                                    >
                                                        <td style={{ textAlign: 'left', paddingLeft: '10px' }}>
                                                            {expandedGroups.has(group.groupId) ?
                                                                <FaChevronDown className="me-2" size={12} /> :
                                                                <FaChevronRight className="me-2" size={12} />
                                                            }
                                                            <strong>{group.groupName}</strong>
                                                        </td>
                                                        <td style={{ textAlign: 'center' }}>{displayValue(group.catchCountInPaper)}</td>
                                                        <td style={{ textAlign: 'center' }}>{displayValue(group.quantitySumInPaper)}</td>
                                                        <td style={{ textAlign: 'center' }}>{displayValue(group.catchCountInBooklet)}</td>
                                                        <td style={{ textAlign: 'center' }}>{displayValue(group.quantitySumInBooklet)}</td>
                                                        <td style={{ textAlign: 'left', paddingLeft: '10px' }}>
                                                            <div className="d-flex flex-wrap gap-1">
                                                                {group.processNames && group.processNames.length > 0 ? (
                                                                    group.processNames.map((processName, idx) => {
                                                                        // Determine badge color based on process name
                                                                        let badgeClass = "bg-warning text-dark";
                                                                        if (processName.toLowerCase().includes('ctp')) {
                                                                            badgeClass = "bg-warning text-dark";
                                                                        } else if (processName.toLowerCase().includes('digital')) {
                                                                            badgeClass = "bg-primary text-white";
                                                                        } else if (processName.toLowerCase().includes('proof')) {
                                                                            badgeClass = "bg-warning text-dark";
                                                                        } else if (processName.toLowerCase().includes('bundling')) {
                                                                            badgeClass = "bg-danger text-white";
                                                                        }

                                                                        return (
                                                                            <span
                                                                                key={idx}
                                                                                className={`badge ${badgeClass}`}
                                                                                style={{ fontSize: '10px', borderRadius: '4px', padding: '4px 8px', whiteSpace: 'nowrap' }}
                                                                            >
                                                                                {processName}
                                                                            </span>
                                                                        );
                                                                    })
                                                                ) : (
                                                                    <span className="badge bg-secondary">N/A</span>
                                                                )}
                                                            </div>
                                                        </td>
                                                    </tr>

                                                    {/* Project Rows - Only shown when group is expanded */}
                                                    {expandedGroups.has(group.groupId) && (
                                                        loadingProjects.has(group.groupId) ? (
                                                            <tr>
                                                                <td colSpan={6} className="text-center py-3">
                                                                    <Spinner size="sm" animation="border" className="me-2" />
                                                                    Loading project data...
                                                                </td>
                                                            </tr>
                                                        ) : (
                                                            projectData[group.groupId] && projectData[group.groupId].length > 0 ? (
                                                                projectData[group.groupId].map((project, projectIndex) => (
                                                                    <React.Fragment key={`project-${projectIndex}`}>
                                                                        {/* Project Row - Clickable */}
                                                                        <tr
                                                                            style={{
                                                                                backgroundColor: expandedProjects.has(`${group.groupId}-${project.projectId}`) ? '#e9ecef' : '#f8f9fa',
                                                                                cursor: 'pointer'
                                                                            }}
                                                                            onClick={() => handleProjectExpansion(group.groupId, project.projectId)}
                                                                        >
                                                                            <td style={{ textAlign: 'left', paddingLeft: '30px' }}>
                                                                                {expandedProjects.has(`${group.groupId}-${project.projectId}`) ?
                                                                                    <FaChevronDown className="me-2" size={10} /> :
                                                                                    <FaChevronRight className="me-2" size={10} />
                                                                                }
                                                                                <strong>{project.projectName}</strong>
                                                                            </td>
                                                                            <td style={{ textAlign: 'center' }}>{displayValue(project.catchCountInPaper)}</td>
                                                                            <td style={{ textAlign: 'center' }}>{displayValue(project.quantitySumInPaper)}</td>
                                                                            <td style={{ textAlign: 'center' }}>{displayValue(project.catchCountInBooklet)}</td>
                                                                            <td style={{ textAlign: 'center' }}>{displayValue(project.quantitySumInBooklet)}</td>
                                                                            <td style={{ textAlign: 'left', paddingLeft: '10px' }}>
                                                                                <div className="d-flex flex-wrap gap-1">
                                                                                    {project.processNames && project.processNames.length > 0 ? (
                                                                                        project.processNames.map((processName, idx) => {
                                                                                            // Determine badge color based on process name
                                                                                            let badgeClass = "bg-warning text-dark";
                                                                                            if (processName.toLowerCase().includes('ctp')) {
                                                                                                badgeClass = "bg-warning text-dark";
                                                                                            } else if (processName.toLowerCase().includes('digital')) {
                                                                                                badgeClass = "bg-primary text-white";
                                                                                            } else if (processName.toLowerCase().includes('proof')) {
                                                                                                badgeClass = "bg-warning text-dark";
                                                                                            } else if (processName.toLowerCase().includes('bundling')) {
                                                                                                badgeClass = "bg-danger text-white";
                                                                                            }

                                                                                            return (
                                                                                                <span
                                                                                                    key={idx}
                                                                                                    className={`badge ${badgeClass}`}
                                                                                                    style={{ fontSize: '10px', borderRadius: '4px', padding: '4px 8px', whiteSpace: 'nowrap' }}
                                                                                                >
                                                                                                    {processName}
                                                                                                </span>
                                                                                            );
                                                                                        })
                                                                                    ) : (
                                                                                        <span className="badge bg-secondary">N/A</span>
                                                                                    )}
                                                                                </div>
                                                                            </td>
                                                                        </tr>

                                                                        {/* Lot Number Rows - Only shown when project is expanded */}
                                                                        {expandedProjects.has(`${group.groupId}-${project.projectId}`) && (
                                                                            loadingLots.has(`${group.groupId}-${project.projectId}`) ? (
                                                                                <tr>
                                                                                    <td colSpan={6} className="text-center py-3" style={{ backgroundColor: '#f0f0f0' }}>
                                                                                        <Spinner size="sm" animation="border" className="me-2" />
                                                                                        Loading lot data...
                                                                                    </td>
                                                                                </tr>
                                                                            ) : (
                                                                                lotData[`${group.groupId}-${project.projectId}`] && lotData[`${group.groupId}-${project.projectId}`].length > 0 ? (
                                                                                    lotData[`${group.groupId}-${project.projectId}`].map((lot, lotIndex) => (
                                                                                        <tr key={`lot-${lotIndex}`} style={{ backgroundColor: '#f0f0f0' }}>
                                                                                            <td style={{ textAlign: 'left', paddingLeft: '50px' }}>
                                                                                                <span className="text-muted">Lot No: </span>
                                                                                                <strong>{lot.lotNo}</strong>
                                                                                            </td>
                                                                                            {/* For paper/booklet columns, we need to determine based on project type */}
                                                                                            {project.catchCountInPaper > 0 ? (
                                                                                                <>
                                                                                                    <td style={{ textAlign: 'center' }}>{displayValue(lot.totalCatchCount)}</td>
                                                                                                    <td style={{ textAlign: 'center' }}>{displayValue(lot.quantitySum)}</td>
                                                                                                    <td style={{ textAlign: 'center' }}></td>
                                                                                                    <td style={{ textAlign: 'center' }}></td>
                                                                                                </>
                                                                                            ) : (
                                                                                                <>
                                                                                                    <td style={{ textAlign: 'center' }}></td>
                                                                                                    <td style={{ textAlign: 'center' }}></td>
                                                                                                    <td style={{ textAlign: 'center' }}>{displayValue(lot.totalCatchCount)}</td>
                                                                                                    <td style={{ textAlign: 'center' }}>{displayValue(lot.quantitySum)}</td>
                                                                                                </>
                                                                                            )}
                                                                                            <td style={{ textAlign: 'left', paddingLeft: '10px' }}>
                                                                                                <div className="d-flex flex-wrap gap-1">
                                                                                                    {lot.processNames && lot.processNames.length > 0 ? (
                                                                                                        lot.processNames.map((processName, idx) => {
                                                                                                            // Determine badge color based on process name
                                                                                                            let badgeClass = "bg-warning text-dark";
                                                                                                            if (processName.toLowerCase().includes('ctp')) {
                                                                                                                badgeClass = "bg-warning text-dark";
                                                                                                            } else if (processName.toLowerCase().includes('digital')) {
                                                                                                                badgeClass = "bg-primary text-white";
                                                                                                            } else if (processName.toLowerCase().includes('proof')) {
                                                                                                                badgeClass = "bg-warning text-dark";
                                                                                                            } else if (processName.toLowerCase().includes('bundling')) {
                                                                                                                badgeClass = "bg-danger text-white";
                                                                                                            }

                                                                                                            return (
                                                                                                                <span
                                                                                                                    key={idx}
                                                                                                                    className={`badge ${badgeClass}`}
                                                                                                                    style={{ fontSize: '10px', borderRadius: '4px', padding: '4px 8px', whiteSpace: 'nowrap' }}
                                                                                                                >
                                                                                                                    {processName}
                                                                                                                </span>
                                                                                                            );
                                                                                                        })
                                                                                                    ) : (
                                                                                                        <span className="badge bg-secondary"></span>
                                                                                                    )}
                                                                                                </div>
                                                                                            </td>
                                                                                        </tr>
                                                                                    ))
                                                                                ) : (
                                                                                    <tr>
                                                                                        <td colSpan={6} className="text-center py-3" style={{ backgroundColor: '#f0f0f0' }}>
                                                                                            No lot data available for this project
                                                                                        </td>
                                                                                    </tr>
                                                                                )
                                                                            )
                                                                        )}
                                                                    </React.Fragment>
                                                                ))
                                                            ) : (
                                                                <tr>
                                                                    <td colSpan={6} className="text-center py-3">
                                                                        No project data available for this group
                                                                    </td>
                                                                </tr>
                                                            )
                                                        )
                                                    )}
                                                </React.Fragment>
                                            ))}
                                        </tbody>
                                    </Table>
                                )}
                                </div>
                            </Card.Body>
                        </Card>
                    )}

                    {/* Toast Notification */}
                    <Toast
                        show={showToast}
                        onClose={() => setShowToast(false)}
                        delay={3000}
                        autohide
                        style={{
                            position: 'fixed',
                            top: '20px',
                            right: '20px',
                            zIndex: 9999
                        }}
                    >
                        <Toast.Header>
                            <strong className="me-auto">Notification</strong>
                        </Toast.Header>
                        <Toast.Body>{toastMessage}</Toast.Body>
                    </Toast>
                </Col>
            </Row>
        </Container>
    );
};

export default Userreport;
