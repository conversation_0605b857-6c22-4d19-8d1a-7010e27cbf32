{"name": "my-react-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ag-grid-community/client-side-row-model": "^32.2.1", "@ag-grid-community/core": "^32.2.2", "@ag-grid-community/react": "^32.2.2", "@ag-grid-community/styles": "^32.2.1", "@ag-grid-enterprise/charts": "^32.2.2", "@ag-grid-enterprise/menu": "^32.2.2", "@canvasjs/react-charts": "^1.0.2", "@coreui/icons": "^3.0.1", "@coreui/react": "^5.3.0", "@emotion/core": "^11.0.0", "@emotion/styled": "^11.13.0", "@fontsource/poppins": "^5.1.0", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@handsontable/react": "^15.2.0", "@nivo/core": "^0.87.0", "@nivo/pie": "^0.87.0", "@tanstack/react-table": "^8.21.2", "@tippyjs/react": "^4.2.6", "ag-grid-community": "^32.2.0", "ag-grid-enterprise": "^33.2.1", "ag-grid-react": "^32.2.0", "antd": "5.3", "apexcharts": "^3.53.0", "axios": "^1.7.7", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "chart.js": "^4.4.4", "chartjs-plugin-datalabels": "^2.2.0", "clipboard": "^2.0.11", "crypto-js": "^4.2.0", "d3": "^7.9.0", "echarts": "^5.5.1", "echarts-for-react": "^3.0.2", "framer-motion": "^11.3.31", "handsontable": "^15.2.0", "highcharts": "^11.4.8", "i18next": "^23.14.0", "i18next-http-backend": "^2.6.2", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "jwt-decode": "^4.0.0", "moment": "^2.29.1", "notistack": "^3.0.1", "plotly.js": "^2.35.2", "react": "^18.3.1", "react-apexcharts": "^1.4.1", "react-audio-voice-recorder": "^2.2.0", "react-beautiful-dnd": "^13.1.1", "react-bootstrap": "^2.10.5", "react-bootstrap-icons": "^1.11.4", "react-chartjs-2": "^5.2.0", "react-circular-progressbar": "^2.1.0", "react-data-table-component": "^7.6.2", "react-datepicker": "^8.3.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-grid-layout": "^1.4.4", "react-highlight-words": "^0.21.0", "react-i18next": "^15.0.1", "react-icons": "^5.3.0", "react-image-crop": "^11.0.7", "react-konva": "^19.0.3", "react-media-recorder": "^1.7.1", "react-plotly.js": "^2.6.0", "react-resizable": "^3.0.5", "react-responsive": "^10.0.0", "react-router": "^6.26.1", "react-router-dom": "^6.26.1", "react-select": "^5.10.1", "react-sortable-hoc": "^2.0.0", "react-svg": "^16.1.34", "react-switch": "^7.0.0", "react-toastify": "^10.0.6", "react-tooltip": "^5.28.0", "react-transition-group": "^4.4.5", "react-voice-recorder": "^2.1.2", "recharts": "^2.12.7", "recorder-js": "^1.0.7", "styled-components": "^6.1.13", "sweetalert2": "^11.14.0", "sweetalert2-react-content": "^5.0.7", "uuid": "^10.0.0", "xlsx": "^0.18.5", "zustand": "^5.0.0-rc.2"}, "devDependencies": {"@babel/plugin-syntax-import-attributes": "^7.25.6", "@eslint/js": "^9.9.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^9.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "vite": "^5.4.1"}}