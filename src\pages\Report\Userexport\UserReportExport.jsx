import * as XLSX from 'xlsx';
import jsPDF from 'jspdf';
import 'jspdf-autotable';

// Helper function to display values (same as in component)
const displayValue = (value) => {
    if (value === null || value === undefined || value === 0 || value === '' || value === '0') {
        return '';
    }
    return value;
};

// Main Excel export function
export const exportUserReportToExcel = (data, reportType, filters, expandedData = {}) => {
    try {
        const workbook = XLSX.utils.book_new();
        
        if (reportType === 'summary') {
            exportSummaryToExcel(workbook, data, filters);
        } else {
            exportDetailsToExcel(workbook, data, filters, expandedData);
        }
        
        // Generate filename with timestamp
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const filename = `User_${reportType}_Report_${timestamp}.xlsx`;
        
        // Save the file
        XLSX.writeFile(workbook, filename);
        
        return { success: true, message: 'Excel file exported successfully!' };
    } catch (error) {
        console.error('Error exporting to Excel:', error);
        return { success: false, message: 'Failed to export Excel file' };
    }
};

// Main PDF export function
export const exportUserReportToPDF = (data, reportType, filters, expandedData = {}) => {
    try {
        const doc = new jsPDF('l', 'mm', 'a4'); // Landscape orientation
        
        if (reportType === 'summary') {
            exportSummaryToPDF(doc, data, filters);
        } else {
            exportDetailsToPDF(doc, data, filters, expandedData);
        }
        
        // Generate filename with timestamp
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const filename = `User_${reportType}_Report_${timestamp}.pdf`;
        
        // Save the file
        doc.save(filename);
        
        return { success: true, message: 'PDF file exported successfully!' };
    } catch (error) {
        console.error('Error exporting to PDF:', error);
        return { success: false, message: 'Failed to export PDF file' };
    }
};

// Excel Export Functions
const exportSummaryToExcel = (workbook, data, filters) => {
    const worksheetData = [
        // Header row
        ['Supervisor', 'Groups', 'Projects', 'Lot No','Catch', 'Total Quantity'],
        // Data rows
        ...data.map(item => [
            item.supervisor || '',
            displayValue(item.catchCount),
            displayValue(item.totalQuantity),
            displayValue(item.countOfGroupIds),
            displayValue(item.countIfProjectIds),
            displayValue(item.countOfLotNo)
        ])
    ];
    
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);
    
    // Set column widths
    worksheet['!cols'] = [
        { width: 20 }, // Supervisor
        { width: 12 }, // Catch Count
        { width: 15 }, // Total Quantity
        { width: 15 }, // Count of Groups
        { width: 15 }, // Count of Projects
        { width: 15 }  // Count of Lot No
    ];
    
    // Add filter info at the top
    const filterInfo = createFilterInfo(filters);
    if (filterInfo.length > 0) {
        XLSX.utils.sheet_add_aoa(worksheet, filterInfo, { origin: 'A1' });
        XLSX.utils.sheet_add_aoa(worksheet, [[''], ['']], { origin: 'A' + (filterInfo.length + 1) });
        XLSX.utils.sheet_add_aoa(worksheet, worksheetData, { origin: 'A' + (filterInfo.length + 3) });
    }
    
    XLSX.utils.book_append_sheet(workbook, worksheet, 'User Summary Report');
};

const exportDetailsToExcel = (workbook, data, filters, expandedData) => {
    const worksheetData = [
        // Main header row
        ['Groups', 'Paper', '', 'Booklet', '', 'Process'],
        // Sub header row
        ['', 'Catch', 'Quantity', 'Catch', 'Quantity', '']
    ];

    // Process hierarchical data
    data.forEach(group => {
        // Add group row
        worksheetData.push([
            group.groupName || '',
            displayValue(group.catchCountInPaper),
            displayValue(group.quantitySumInPaper),
            displayValue(group.catchCountInBooklet),
            displayValue(group.quantitySumInBooklet),
            group.processNames ? group.processNames.join(', ') : ''
        ]);

        // Add project rows if available in expandedData
        const projectData = expandedData.projectData && expandedData.projectData[group.groupId];
        if (projectData && projectData.length > 0) {
            projectData.forEach(project => {
                worksheetData.push([
                    `  ${project.projectName || ''}`, // Indented with spaces
                    displayValue(project.catchCountInPaper),
                    displayValue(project.quantitySumInPaper),
                    displayValue(project.catchCountInBooklet),
                    displayValue(project.quantitySumInBooklet),
                    project.processNames ? project.processNames.join(', ') : ''
                ]);

                // Add lot rows if available in expandedData
                const lotData = expandedData.lotData && expandedData.lotData[`${group.groupId}-${project.projectId}`];
                if (lotData && lotData.length > 0) {
                    lotData.forEach(lot => {
                        const paperCatch = project.catchCountInPaper > 0 ? displayValue(lot.totalCatchCount) : '';
                        const paperQuantity = project.catchCountInPaper > 0 ? displayValue(lot.quantitySum) : '';
                        const bookletCatch = project.catchCountInPaper > 0 ? '' : displayValue(lot.totalCatchCount);
                        const bookletQuantity = project.catchCountInPaper > 0 ? '' : displayValue(lot.quantitySum);

                        worksheetData.push([
                            `    Lot No: ${lot.lotNo}`, // More indented with spaces
                            paperCatch,
                            paperQuantity,
                            bookletCatch,
                            bookletQuantity,
                            lot.processNames ? lot.processNames.join(', ') : ''
                        ]);
                    });
                }
            });
        }
    });
    
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);
    
    // Set column widths
    worksheet['!cols'] = [
        { width: 35 }, // Groups (with indentation)
        { width: 12 }, // Paper Catch
        { width: 15 }, // Paper Quantity
        { width: 12 }, // Booklet Catch
        { width: 15 }, // Booklet Quantity
        { width: 30 }  // Process
    ];
    
    // Add filter info at the top
    const filterInfo = createFilterInfo(filters);
    if (filterInfo.length > 0) {
        XLSX.utils.sheet_add_aoa(worksheet, filterInfo, { origin: 'A1' });
        XLSX.utils.sheet_add_aoa(worksheet, [[''], ['']], { origin: 'A' + (filterInfo.length + 1) });
        XLSX.utils.sheet_add_aoa(worksheet, worksheetData, { origin: 'A' + (filterInfo.length + 3) });

        // Merge cells for main headers
        const headerStartRow = filterInfo.length + 3;
        worksheet['!merges'] = [
            { s: { r: headerStartRow - 1, c: 1 }, e: { r: headerStartRow - 1, c: 2 } }, // Paper header
            { s: { r: headerStartRow - 1, c: 3 }, e: { r: headerStartRow - 1, c: 4 } }, // Booklet header
            { s: { r: headerStartRow - 1, c: 0 }, e: { r: headerStartRow, c: 0 } },     // Groups header
            { s: { r: headerStartRow - 1, c: 5 }, e: { r: headerStartRow, c: 5 } }      // Process header
        ];
    } else {
        // Merge cells for main headers when no filter info
        worksheet['!merges'] = [
            { s: { r: 0, c: 1 }, e: { r: 0, c: 2 } }, // Paper header
            { s: { r: 0, c: 3 }, e: { r: 0, c: 4 } }, // Booklet header
            { s: { r: 0, c: 0 }, e: { r: 1, c: 0 } }, // Groups header
            { s: { r: 0, c: 5 }, e: { r: 1, c: 5 } }  // Process header
        ];
    }
    
    XLSX.utils.book_append_sheet(workbook, worksheet, 'User Details Report');
};

// PDF Export Functions
const exportSummaryToPDF = (doc, data, filters) => {
    // Add title
    doc.setFontSize(16);
    doc.text('User Summary Report', 14, 20);
    
    // Add filter information
    let yPosition = 30;
    const filterInfo = createFilterInfo(filters);
    filterInfo.forEach(info => {
        doc.setFontSize(10);
        doc.text(info[0], 14, yPosition);
        yPosition += 6;
    });
    
    // Prepare table data
    const tableData = data.map(item => [
        item.supervisor || '',
        displayValue(item.catchCount),
        displayValue(item.totalQuantity),
        displayValue(item.countOfGroupIds),
        displayValue(item.countIfProjectIds),
        displayValue(item.countOfLotNo)
    ]);
    
    // Add table
    doc.autoTable({
        head: [['Supervisor', 'Groups', 'Projects', 'Lot No','Catch', 'Total Quantity' ]],
        body: tableData,
        startY: yPosition + 5,
        styles: { fontSize: 8, cellPadding: 2 },
        headStyles: { fillColor: [52, 58, 64], textColor: 255 },
        columnStyles: {
            0: { cellWidth: 40 },
            1: { cellWidth: 25 },
            2: { cellWidth: 30 },
            3: { cellWidth: 30 },
            4: { cellWidth: 30 },
            5: { cellWidth: 25 }
        }
    });
};

const exportDetailsToPDF = (doc, data, filters, expandedData) => {
    // Add title
    doc.setFontSize(16);
    doc.text('User Details Report', 14, 20);
    
    // Add filter information
    let yPosition = 30;
    const filterInfo = createFilterInfo(filters);
    filterInfo.forEach(info => {
        doc.setFontSize(10);
        doc.text(info[0], 14, yPosition);
        yPosition += 6;
    });
    
    // Prepare hierarchical table data
    const tableData = [];

    data.forEach(group => {
        // Add group row
        tableData.push([
            group.groupName || '',
            displayValue(group.catchCountInPaper),
            displayValue(group.quantitySumInPaper),
            displayValue(group.catchCountInBooklet),
            displayValue(group.quantitySumInBooklet),
            group.processNames ? group.processNames.join(', ') : ''
        ]);

        // Add project rows if available
        const projectData = expandedData.projectData && expandedData.projectData[group.groupId];
        if (projectData && projectData.length > 0) {
            projectData.forEach(project => {
                tableData.push([
                    `  ${project.projectName || ''}`, // Indented
                    displayValue(project.catchCountInPaper),
                    displayValue(project.quantitySumInPaper),
                    displayValue(project.catchCountInBooklet),
                    displayValue(project.quantitySumInBooklet),
                    project.processNames ? project.processNames.join(', ') : ''
                ]);

                // Add lot rows if available
                const lotData = expandedData.lotData && expandedData.lotData[`${group.groupId}-${project.projectId}`];
                if (lotData && lotData.length > 0) {
                    lotData.forEach(lot => {
                        const paperCatch = project.catchCountInPaper > 0 ? displayValue(lot.totalCatchCount) : '';
                        const paperQuantity = project.catchCountInPaper > 0 ? displayValue(lot.quantitySum) : '';
                        const bookletCatch = project.catchCountInPaper > 0 ? '' : displayValue(lot.totalCatchCount);
                        const bookletQuantity = project.catchCountInPaper > 0 ? '' : displayValue(lot.quantitySum);

                        tableData.push([
                            `    Lot No: ${lot.lotNo}`, // More indented
                            paperCatch,
                            paperQuantity,
                            bookletCatch,
                            bookletQuantity,
                            lot.processNames ? lot.processNames.join(', ') : ''
                        ]);
                    });
                }
            });
        }
    });
    
    // Add table with styling for different levels
    doc.autoTable({
        head: [
            ['Groups', 'Paper', '', 'Booklet', '', 'Process'],
            ['', 'Catch', 'Quantity', 'Catch', 'Quantity', '']
        ],
        body: tableData,
        startY: yPosition + 5,
        styles: { fontSize: 7, cellPadding: 1.5 },
        headStyles: { fillColor: [52, 58, 64], textColor: 255 },
        columnStyles: {
            0: { cellWidth: 70 }, // Groups (wider for indentation)
            1: { cellWidth: 25 }, // Paper Catch
            2: { cellWidth: 30 }, // Paper Quantity
            3: { cellWidth: 25 }, // Booklet Catch
            4: { cellWidth: 30 }, // Booklet Quantity
            5: { cellWidth: 60 }  // Process
        },
        didParseCell: function(data) {
            // Style different levels based on indentation
            if (data.row.index >= 0) {
                const cellText = data.row.raw[0];
                if (cellText && typeof cellText === 'string') {
                    if (!cellText.startsWith(' ')) {
                        // Group level (no indentation)
                        data.cell.styles.fillColor = [248, 249, 250];
                        data.cell.styles.fontStyle = 'bold';
                    } else if (cellText.startsWith('  ') && !cellText.startsWith('    ')) {
                        // Project level (2 spaces)
                        data.cell.styles.fillColor = [233, 236, 239];
                    } else if (cellText.startsWith('    ')) {
                        // Lot level (4 spaces)
                        data.cell.styles.fillColor = [240, 240, 240];
                    }
                }
            }
        }
    });
};

// Create filter information for the report
const createFilterInfo = (filters) => {
    const filterInfo = [];
    
    if (filters.startDate) {
        if (filters.endDate) {
            filterInfo.push([`Date Range: ${filters.startDate} to ${filters.endDate}`]);
        } else {
            filterInfo.push([`Date: ${filters.startDate}`]);
        }
    }
    
    if (filters.selectedUser && filters.selectedUser !== 'all') {
        filterInfo.push([`User: ${filters.selectedUser}`]);
    }
    
    if (filters.reportType) {
        filterInfo.push([`Report Type: ${filters.reportType.charAt(0).toUpperCase() + filters.reportType.slice(1)}`]);
    }
    
    filterInfo.push([`Generated: ${new Date().toLocaleString()}`]);
    
    return filterInfo;
};
