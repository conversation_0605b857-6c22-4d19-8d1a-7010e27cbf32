import * as XLSX from 'xlsx';
import jsPDF from 'jspdf';
import 'jspdf-autotable';

// Helper function to display values (same as in component)
const displayValue = (value) => {
    if (value === null || value === undefined || value === 0 || value === '' || value === '0') {
        return '';
    }
    return value;
};

// Main Excel export function
export const exportUserReportToExcel = (data, reportType, filters, expandedData = {}) => {
    try {
        // Validate input data
        if (!data || !Array.isArray(data) || data.length === 0) {
            return { success: false, message: 'No data available to export' };
        }

        const workbook = XLSX.utils.book_new();

        if (reportType === 'summary') {
            exportSummaryToExcel(workbook, data, filters);
        } else {
            exportDetailsToExcel(workbook, data, filters, expandedData);
        }

        // Generate descriptive filename
        const filename = generateFileName('Excel', reportType, filters);

        // Save the file
        XLSX.writeFile(workbook, filename);

        return {
            success: true,
            message: `Excel file "${filename}" exported successfully!`,
            filename: filename
        };
    } catch (error) {
        console.error('Error exporting to Excel:', error);
        return {
            success: false,
            message: `Failed to export Excel file: ${error.message || 'Unknown error'}`
        };
    }
};

// Main PDF export function
export const exportUserReportToPDF = (data, reportType, filters, expandedData = {}) => {
    try {
        // Validate input data
        if (!data || !Array.isArray(data) || data.length === 0) {
            return { success: false, message: 'No data available to export' };
        }

        const doc = new jsPDF('l', 'mm', 'a4'); // Landscape orientation

        if (reportType === 'summary') {
            exportSummaryToPDF(doc, data, filters);
        } else {
            exportDetailsToPDF(doc, data, filters, expandedData);
        }

        // Generate descriptive filename
        const filename = generateFileName('PDF', reportType, filters);

        // Save the file
        doc.save(filename);

        return {
            success: true,
            message: `PDF file "${filename}" exported successfully!`,
            filename: filename
        };
    } catch (error) {
        console.error('Error exporting to PDF:', error);
        return {
            success: false,
            message: `Failed to export PDF file: ${error.message || 'Unknown error'}`
        };
    }
};

// Excel Export Functions
const exportSummaryToExcel = (workbook, data, filters) => {
    const headerConfig = getSummaryHeaders();
    const worksheetData = [
        // Header row using helper function
        headerConfig.headers,
        // Data rows using consistent mapping
        ...data.map(headerConfig.dataMapping)
    ];
    
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);
    
    // Set column widths - Fixed to match correct column order
    worksheet['!cols'] = [
        { width: 20 }, // Supervisor
        { width: 15 }, // Groups
        { width: 15 }, // Projects
        { width: 15 }, // Lot No
        { width: 12 }, // Catch Count
        { width: 15 }  // Total Quantity
    ];
    
    // Add filter info at the top
    const filterInfo = createFilterInfo(filters);
    if (filterInfo.length > 0) {
        XLSX.utils.sheet_add_aoa(worksheet, filterInfo, { origin: 'A1' });
        XLSX.utils.sheet_add_aoa(worksheet, [[''], ['']], { origin: 'A' + (filterInfo.length + 1) });
        XLSX.utils.sheet_add_aoa(worksheet, worksheetData, { origin: 'A' + (filterInfo.length + 3) });
    }
    
    XLSX.utils.book_append_sheet(workbook, worksheet, 'User Summary Report');
};

const exportDetailsToExcel = (workbook, data, filters, expandedData) => {
    const headerConfig = getDetailsHeaders();
    const worksheetData = [
        // Main header row using helper function
        headerConfig.mainHeaders,
        // Sub header row using helper function
        headerConfig.subHeaders
    ];

    // Process hierarchical data
    data.forEach(group => {
        // Add group row
        worksheetData.push([
            group.groupName || '',
            displayValue(group.catchCountInPaper),
            displayValue(group.quantitySumInPaper),
            displayValue(group.catchCountInBooklet),
            displayValue(group.quantitySumInBooklet),
            group.processNames ? group.processNames.join(', ') : ''
        ]);

        // Add project rows if available in expandedData
        const projectData = expandedData.projectData && expandedData.projectData[group.groupId];
        if (projectData && projectData.length > 0) {
            projectData.forEach(project => {
                worksheetData.push([
                    `  ${project.projectName || ''}`, // Indented with spaces
                    displayValue(project.catchCountInPaper),
                    displayValue(project.quantitySumInPaper),
                    displayValue(project.catchCountInBooklet),
                    displayValue(project.quantitySumInBooklet),
                    project.processNames ? project.processNames.join(', ') : ''
                ]);

                // Add lot rows if available in expandedData
                const lotData = expandedData.lotData && expandedData.lotData[`${group.groupId}-${project.projectId}`];
                if (lotData && lotData.length > 0) {
                    lotData.forEach(lot => {
                        const paperCatch = project.catchCountInPaper > 0 ? displayValue(lot.totalCatchCount) : '';
                        const paperQuantity = project.catchCountInPaper > 0 ? displayValue(lot.quantitySum) : '';
                        const bookletCatch = project.catchCountInPaper > 0 ? '' : displayValue(lot.totalCatchCount);
                        const bookletQuantity = project.catchCountInPaper > 0 ? '' : displayValue(lot.quantitySum);

                        worksheetData.push([
                            `    Lot No: ${lot.lotNo}`, // More indented with spaces
                            paperCatch,
                            paperQuantity,
                            bookletCatch,
                            bookletQuantity,
                            lot.processNames ? lot.processNames.join(', ') : ''
                        ]);
                    });
                }
            });
        }
    });
    
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);
    
    // Set column widths - Optimized for better readability
    worksheet['!cols'] = [
        { width: 40 }, // Groups (with indentation for hierarchy)
        { width: 15 }, // Paper Catch Count
        { width: 18 }, // Paper Quantity
        { width: 15 }, // Booklet Catch Count
        { width: 18 }, // Booklet Quantity
        { width: 35 }  // Process Names
    ];
    
    // Add filter info at the top
    const filterInfo = createFilterInfo(filters);
    if (filterInfo.length > 0) {
        XLSX.utils.sheet_add_aoa(worksheet, filterInfo, { origin: 'A1' });
        XLSX.utils.sheet_add_aoa(worksheet, [[''], ['']], { origin: 'A' + (filterInfo.length + 1) });
        XLSX.utils.sheet_add_aoa(worksheet, worksheetData, { origin: 'A' + (filterInfo.length + 3) });

        // Merge cells for main headers - Improved header merging
        const headerStartRow = filterInfo.length + 3;
        worksheet['!merges'] = [
            { s: { r: headerStartRow - 1, c: 1 }, e: { r: headerStartRow - 1, c: 2 } }, // Paper Format header
            { s: { r: headerStartRow - 1, c: 3 }, e: { r: headerStartRow - 1, c: 4 } }, // Booklet Format header
            { s: { r: headerStartRow - 1, c: 0 }, e: { r: headerStartRow, c: 0 } },     // Groups header (spans 2 rows)
            { s: { r: headerStartRow - 1, c: 5 }, e: { r: headerStartRow, c: 5 } }      // Process header (spans 2 rows)
        ];
    } else {
        // Merge cells for main headers when no filter info
        worksheet['!merges'] = [
            { s: { r: 0, c: 1 }, e: { r: 0, c: 2 } }, // Paper Format header
            { s: { r: 0, c: 3 }, e: { r: 0, c: 4 } }, // Booklet Format header
            { s: { r: 0, c: 0 }, e: { r: 1, c: 0 } }, // Groups header (spans 2 rows)
            { s: { r: 0, c: 5 }, e: { r: 1, c: 5 } }  // Process header (spans 2 rows)
        ];
    }
    
    XLSX.utils.book_append_sheet(workbook, worksheet, 'User Details Report');
};

// PDF Export Functions
const exportSummaryToPDF = (doc, data, filters) => {
    // Add title
    doc.setFontSize(16);
    doc.text('User Summary Report', 14, 20);
    
    // Add filter information
    let yPosition = 30;
    const filterInfo = createFilterInfo(filters);
    filterInfo.forEach(info => {
        doc.setFontSize(10);
        doc.text(info[0], 14, yPosition);
        yPosition += 6;
    });
    
    // Prepare table data using helper function for consistency
    const headerConfig = getSummaryHeaders();
    const tableData = data.map(headerConfig.dataMapping);

    // Add table using helper function for consistent headers
    doc.autoTable({
        head: [headerConfig.headers],
        body: tableData,
        startY: yPosition + 5,
        styles: { fontSize: 8, cellPadding: 2, lineWidth: 0.1 },
        headStyles: {
            fillColor: [52, 58, 64],
            textColor: 255,
            fontStyle: 'bold',
            halign: 'center'
        },
        bodyStyles: {
            halign: 'center'
        },
        columnStyles: {
            0: { cellWidth: 40, halign: 'left' }, // Supervisor - left aligned
            1: { cellWidth: 25 }, // Groups
            2: { cellWidth: 30 }, // Projects
            3: { cellWidth: 25 }, // Lot No
            4: { cellWidth: 25 }, // Catch
            5: { cellWidth: 30 }  // Total Quantity
        },
        theme: 'striped',
        alternateRowStyles: { fillColor: [248, 249, 250] }
    });
};

const exportDetailsToPDF = (doc, data, filters, expandedData) => {
    // Add title
    doc.setFontSize(16);
    doc.text('User Details Report', 14, 20);
    
    // Add filter information
    let yPosition = 30;
    const filterInfo = createFilterInfo(filters);
    filterInfo.forEach(info => {
        doc.setFontSize(10);
        doc.text(info[0], 14, yPosition);
        yPosition += 6;
    });
    
    // Prepare hierarchical table data
    const tableData = [];

    data.forEach(group => {
        // Add group row
        tableData.push([
            group.groupName || '',
            displayValue(group.catchCountInPaper),
            displayValue(group.quantitySumInPaper),
            displayValue(group.catchCountInBooklet),
            displayValue(group.quantitySumInBooklet),
            group.processNames ? group.processNames.join(', ') : ''
        ]);

        // Add project rows if available
        const projectData = expandedData.projectData && expandedData.projectData[group.groupId];
        if (projectData && projectData.length > 0) {
            projectData.forEach(project => {
                tableData.push([
                    `  ${project.projectName || ''}`, // Indented
                    displayValue(project.catchCountInPaper),
                    displayValue(project.quantitySumInPaper),
                    displayValue(project.catchCountInBooklet),
                    displayValue(project.quantitySumInBooklet),
                    project.processNames ? project.processNames.join(', ') : ''
                ]);

                // Add lot rows if available
                const lotData = expandedData.lotData && expandedData.lotData[`${group.groupId}-${project.projectId}`];
                if (lotData && lotData.length > 0) {
                    lotData.forEach(lot => {
                        const paperCatch = project.catchCountInPaper > 0 ? displayValue(lot.totalCatchCount) : '';
                        const paperQuantity = project.catchCountInPaper > 0 ? displayValue(lot.quantitySum) : '';
                        const bookletCatch = project.catchCountInPaper > 0 ? '' : displayValue(lot.totalCatchCount);
                        const bookletQuantity = project.catchCountInPaper > 0 ? '' : displayValue(lot.quantitySum);

                        tableData.push([
                            `    Lot No: ${lot.lotNo}`, // More indented
                            paperCatch,
                            paperQuantity,
                            bookletCatch,
                            bookletQuantity,
                            lot.processNames ? lot.processNames.join(', ') : ''
                        ]);
                    });
                }
            });
        }
    });
    
    // Add table with improved styling using helper functions
    const headerConfig = getDetailsHeaders();
    doc.autoTable({
        head: [
            headerConfig.mainHeaders,
            headerConfig.subHeaders
        ],
        body: tableData,
        startY: yPosition + 5,
        styles: {
            fontSize: 7,
            cellPadding: 2,
            lineWidth: 0.1,
            lineColor: [200, 200, 200]
        },
        headStyles: {
            fillColor: [52, 58, 64],
            textColor: 255,
            fontStyle: 'bold',
            halign: 'center'
        },
        columnStyles: {
            0: { cellWidth: 65, halign: 'left' }, // Groups (wider for indentation, left-aligned)
            1: { cellWidth: 28, halign: 'center' }, // Paper Catch Count
            2: { cellWidth: 32, halign: 'center' }, // Paper Quantity
            3: { cellWidth: 28, halign: 'center' }, // Booklet Catch Count
            4: { cellWidth: 32, halign: 'center' }, // Booklet Quantity
            5: { cellWidth: 55, halign: 'left' }    // Process Names
        },
        didParseCell: function(data) {
            // Enhanced styling for different hierarchy levels
            if (data.row.index >= 0) {
                const cellText = data.row.raw[0];
                if (cellText && typeof cellText === 'string') {
                    if (!cellText.startsWith(' ')) {
                        // Group level (no indentation) - Bold with light background
                        data.cell.styles.fillColor = [240, 248, 255];
                        data.cell.styles.fontStyle = 'bold';
                        data.cell.styles.textColor = [0, 0, 0];
                    } else if (cellText.startsWith('  ') && !cellText.startsWith('    ')) {
                        // Project level (2 spaces) - Medium background
                        data.cell.styles.fillColor = [248, 249, 250];
                        data.cell.styles.fontStyle = 'normal';
                        data.cell.styles.textColor = [33, 37, 41];
                    } else if (cellText.startsWith('    ')) {
                        // Lot level (4 spaces) - Lightest background
                        data.cell.styles.fillColor = [253, 253, 253];
                        data.cell.styles.fontStyle = 'normal';
                        data.cell.styles.textColor = [108, 117, 125];
                    }
                }
            }
        },
        theme: 'grid',
        tableLineColor: [200, 200, 200],
        tableLineWidth: 0.1
    });
};

// Create filter information for the report
const createFilterInfo = (filters) => {
    const filterInfo = [];

    if (filters.startDate) {
        if (filters.endDate) {
            filterInfo.push([`Date Range: ${formatDateForDisplay(filters.startDate)} to ${formatDateForDisplay(filters.endDate)}`]);
        } else {
            filterInfo.push([`Date: ${formatDateForDisplay(filters.startDate)}`]);
        }
    }

    if (filters.selectedUser && filters.selectedUser !== 'all') {
        filterInfo.push([`Selected User: ${filters.selectedUser}`]);
    }

    if (filters.reportType) {
        filterInfo.push([`Report Type: ${filters.reportType.charAt(0).toUpperCase() + filters.reportType.slice(1)} Report`]);
    }

    filterInfo.push([`Generated On: ${new Date().toLocaleString()}`]);
    filterInfo.push(['']); // Empty line for spacing

    return filterInfo;
};

// Helper function to format date for display
const formatDateForDisplay = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
};

// Helper function to create summary headers mapping
const getSummaryHeaders = () => ({
    headers: ['Supervisor', 'Groups', 'Projects', 'Lot No', 'Catch', 'Total Quantity'],
    dataMapping: (item) => [
        item.supervisor || '',
        displayValue(item.countOfGroupIds),
        displayValue(item.countIfProjectIds),
        displayValue(item.countOfLotNo),
        displayValue(item.catchCount),
        displayValue(item.totalQuantity)
    ]
});

// Helper function to create details headers mapping
const getDetailsHeaders = () => ({
    mainHeaders: ['Groups', 'Paper Format', '', 'Booklet Format', '', 'Process'],
    subHeaders: ['', 'Catch Count', 'Quantity', 'Catch Count', 'Quantity', 'Names'],
    dataMapping: (item) => [
        item.groupName || item.projectName || item.lotNo || '',
        displayValue(item.catchCountInPaper),
        displayValue(item.quantitySumInPaper),
        displayValue(item.catchCountInBooklet),
        displayValue(item.quantitySumInBooklet),
        item.processNames ? item.processNames.join(', ') : ''
    ]
});
